<?php

require_once 'ImageProcessor.php';

/**
 * Main entry point for the image processing application
 */

// Load configuration
$config = require 'config.php';

// Initialize the image processor
$processor = new ImageProcessor($config);

// Handle different request methods
$method = isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'CLI';

// Set content type to JSON (only for web requests)
if ($method !== 'CLI') {
    header('Content-Type: application/json');
}

try {
    switch ($method) {
        case 'CLI':
            // Command line execution - show status
            echo "Image Processor API\n";
            echo "==================\n\n";
            $status = $processor->getStatus();
            echo "Status:\n";
            echo "- Qwen configured: " . ($status['qwen_configured'] ? 'Yes' : 'No') . "\n";
            echo "- DeepSeek configured: " . ($status['deepseek_configured'] ? 'Yes' : 'No') . "\n";
            echo "- Log file: " . $status['log_file'] . "\n";
            echo "\nTo use the web API, start the server with: php -S localhost:8000\n";
            break;

        case 'GET':
            // Return status information
            $status = $processor->getStatus();
            echo json_encode([
                'success' => true,
                'message' => 'Image Processor API is running',
                'status' => $status
            ]);
            break;
            
        case 'POST':
            // Process image URL
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['image_url'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Missing image_url parameter'
                ]);
                break;
            }
            
            $imageUrl = $input['image_url'];
            
            // Validate image URL
            if (!$processor->validateImageUrl($imageUrl)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid image URL'
                ]);
                break;
            }
            
            // Process the image
            $result = $processor->processImage($imageUrl);
            
            echo json_encode($result);
            break;
            
        default:
            if ($method !== 'CLI') {
                http_response_code(405);
                echo json_encode([
                    'success' => false,
                    'error' => 'Method not allowed'
                ]);
            }
            break;
    }

} catch (Exception $e) {
    if ($method !== 'CLI') {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    } else {
        echo "Error: " . $e->getMessage() . "\n";
    }
}
