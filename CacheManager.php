<?php

/**
 * Cache Manager for storing and retrieving processed results
 */
class CacheManager
{
    private $cacheDir;
    private $logger;
    private $ttl; // Time to live in seconds
    
    public function __construct($config, $logger)
    {
        $this->logger = $logger;
        $this->cacheDir = isset($config['cache']['directory']) ? $config['cache']['directory'] : 'cache';
        $this->ttl = isset($config['cache']['ttl']) ? $config['cache']['ttl'] : 3600; // 1 hour default
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Generate cache key for image URL
     *
     * @param string $imageUrl
     * @return string
     */
    private function getCacheKey($imageUrl)
    {
        return md5($imageUrl);
    }
    
    /**
     * Get cache file path
     *
     * @param string $key
     * @return string
     */
    private function getCacheFilePath($key)
    {
        return $this->cacheDir . '/' . $key . '.json';
    }
    
    /**
     * Check if cached result exists and is valid
     *
     * @param string $imageUrl
     * @return bool
     */
    public function has($imageUrl)
    {
        $key = $this->getCacheKey($imageUrl);
        $filePath = $this->getCacheFilePath($key);
        
        if (!file_exists($filePath)) {
            return false;
        }
        
        // Check if cache is expired
        $fileTime = filemtime($filePath);
        if (time() - $fileTime > $this->ttl) {
            $this->delete($imageUrl);
            return false;
        }
        
        return true;
    }
    
    /**
     * Get cached result
     *
     * @param string $imageUrl
     * @return array|null
     */
    public function get($imageUrl)
    {
        if (!$this->has($imageUrl)) {
            return null;
        }
        
        $key = $this->getCacheKey($imageUrl);
        $filePath = $this->getCacheFilePath($key);
        
        $content = file_get_contents($filePath);
        $data = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->error("Cache JSON decode error for {$imageUrl}: " . json_last_error_msg());
            $this->delete($imageUrl);
            return null;
        }
        
        $this->logger->info("Cache hit for image", ['image_url' => $imageUrl]);
        return $data;
    }
    
    /**
     * Store result in cache
     *
     * @param string $imageUrl
     * @param array $result
     * @return bool
     */
    public function set($imageUrl, $result)
    {
        $key = $this->getCacheKey($imageUrl);
        $filePath = $this->getCacheFilePath($key);
        
        $cacheData = [
            'image_url' => $imageUrl,
            'timestamp' => time(),
            'result' => $result
        ];
        
        $success = file_put_contents($filePath, json_encode($cacheData, JSON_PRETTY_PRINT));
        
        if ($success) {
            $this->logger->info("Result cached for image", ['image_url' => $imageUrl]);
            return true;
        } else {
            $this->logger->error("Failed to cache result for image", ['image_url' => $imageUrl]);
            return false;
        }
    }
    
    /**
     * Delete cached result
     *
     * @param string $imageUrl
     * @return bool
     */
    public function delete($imageUrl)
    {
        $key = $this->getCacheKey($imageUrl);
        $filePath = $this->getCacheFilePath($key);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     *
     * @return bool
     */
    public function clear()
    {
        $files = glob($this->cacheDir . '/*.json');
        $success = true;
        
        foreach ($files as $file) {
            if (!unlink($file)) {
                $success = false;
            }
        }
        
        $this->logger->info("Cache cleared", ['files_removed' => count($files)]);
        return $success;
    }
    
    /**
     * Get cache statistics
     *
     * @return array
     */
    public function getStats()
    {
        $files = glob($this->cacheDir . '/*.json');
        $totalSize = 0;
        $validFiles = 0;
        $expiredFiles = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            $fileTime = filemtime($file);
            
            if (time() - $fileTime > $this->ttl) {
                $expiredFiles++;
            } else {
                $validFiles++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_files' => $validFiles,
            'expired_files' => $expiredFiles,
            'total_size_bytes' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'cache_directory' => $this->cacheDir,
            'ttl_seconds' => $this->ttl
        ];
    }
}
