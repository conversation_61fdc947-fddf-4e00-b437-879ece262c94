<?php

require_once 'Logger.php';
require_once 'QwenService.php';
require_once 'DeepSeekService.php';

/**
 * Main Image Processor class that orchestrates the workflow
 */
class ImageProcessor
{
    private $config;
    private $logger;
    private $qwenService;
    private $deepSeekService;
    
    public function __construct($config)
    {
        $this->config = $config;
        $this->logger = new Logger($config);
        $this->qwenService = new QwenService($config, $this->logger);
        $this->deepSeekService = new DeepSeekService($config, $this->logger);
    }
    
    /**
     * Process image URL through the complete workflow
     *
     * @param string $imageUrl
     * @return array
     * @throws Exception
     */
    public function processImage($imageUrl)
    {
        $this->logger->info("Starting image processing workflow", ['image_url' => $imageUrl]);
        
        try {
            // Step 1: Process image with Qwen VL Plus
            $this->logger->info("Step 1: Processing image with Qwen VL Plus");
            $qwenResponse = $this->qwenService->processImage($imageUrl);
            
            // Extract text from Qwen response
            $qwenText = $this->qwenService->extractTextFromResponse($qwenResponse);
            $this->logger->info("Qwen text extracted", ['text_length' => strlen($qwenText)]);
            
            // Step 2: Process Qwen result with DeepSeek Chat
            $this->logger->info("Step 2: Processing Qwen result with DeepSeek Chat");
            $deepSeekResponse = $this->deepSeekService->processText($qwenText);
            
            // Extract text from DeepSeek response
            $deepSeekText = $this->deepSeekService->extractTextFromResponse($deepSeekResponse);
            $this->logger->info("DeepSeek text extracted", ['text_length' => strlen($deepSeekText)]);
            
            // Step 3: Log both raw responses
            $this->logger->info("Step 3: Logging raw responses to file");
            $this->logRawResponses($qwenResponse, $deepSeekResponse);
            
            $result = [
                'success' => true,
                'qwen_response' => $qwenResponse,
                'deepseek_response' => $deepSeekResponse,
                'qwen_text' => $qwenText,
                'deepseek_text' => $deepSeekText
            ];
            
            $this->logger->info("Image processing workflow completed successfully");
            
            return $result;
            
        } catch (Exception $e) {
            $this->logger->error("Image processing workflow failed: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Log raw responses from both APIs
     *
     * @param array $qwenResponse
     * @param array $deepSeekResponse
     */
    private function logRawResponses($qwenResponse, $deepSeekResponse)
    {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'qwen_raw_response' => $qwenResponse['raw_response'],
            'deepseek_raw_response' => $deepSeekResponse['raw_response']
        ];
        
        $this->logger->info("Raw API Responses", $logEntry);
    }
    
    /**
     * Validate image URL
     *
     * @param string $imageUrl
     * @return bool
     */
    public function validateImageUrl($imageUrl)
    {
        if (empty($imageUrl)) {
            return false;
        }
        
        if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // Check if URL points to an image
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        $urlPath = parse_url($imageUrl, PHP_URL_PATH);
        $extension = strtolower(pathinfo($urlPath, PATHINFO_EXTENSION));
        
        return in_array($extension, $imageExtensions);
    }
    
    /**
     * Get processing status
     *
     * @return array
     */
    public function getStatus()
    {
        return [
            'qwen_configured' => !empty($this->config['qwen']['api_key']),
            'deepseek_configured' => !empty($this->config['deepseek']['api_key']),
            'log_file' => $this->config['logging']['log_file'],
            'log_file_exists' => file_exists($this->config['logging']['log_file'])
        ];
    }
}
