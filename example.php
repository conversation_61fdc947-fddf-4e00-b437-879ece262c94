<?php

require_once 'ImageProcessor.php';

/**
 * Example usage of the Image Processor
 */

// Load configuration
$config = require 'config.php';

// Example image URL (replace with actual image URL)
$imageUrl = 'https://example.com/sample-image.jpg';

echo "Image Processing Example\n";
echo "========================\n\n";

try {
    // Initialize the image processor
    $processor = new ImageProcessor($config);
    
    // Check status
    echo "Checking system status...\n";
    $status = $processor->getStatus();
    
    echo "Qwen API configured: " . ($status['qwen_configured'] ? 'Yes' : 'No') . "\n";
    echo "DeepSeek API configured: " . ($status['deepseek_configured'] ? 'Yes' : 'No') . "\n";
    echo "Log file: " . $status['log_file'] . "\n\n";
    
    if (!$status['qwen_configured'] || !$status['deepseek_configured']) {
        echo "Error: Please configure API keys in config.php\n";
        exit(1);
    }
    
    // Validate image URL
    echo "Validating image URL: {$imageUrl}\n";
    if (!$processor->validateImageUrl($imageUrl)) {
        echo "Error: Invalid image URL\n";
        exit(1);
    }
    echo "Image URL is valid\n\n";
    
    // Process the image
    echo "Starting image processing workflow...\n";
    $result = $processor->processImage($imageUrl);
    
    if ($result['success']) {
        echo "Processing completed successfully!\n\n";
        
        echo "Qwen VL Plus Response:\n";
        echo "HTTP Code: " . $result['qwen_response']['http_code'] . "\n";
        echo "Text: " . $result['qwen_text'] . "\n\n";
        
        echo "DeepSeek Chat Response:\n";
        echo "HTTP Code: " . $result['deepseek_response']['http_code'] . "\n";
        echo "Text: " . $result['deepseek_text'] . "\n\n";
        
        echo "Raw responses have been logged to: " . $config['logging']['log_file'] . "\n";
    } else {
        echo "Processing failed\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nExample completed.\n";
