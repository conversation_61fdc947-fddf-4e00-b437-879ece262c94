# Image Processing Application

A PHP application that processes images through Qwen VL Plus and DeepSeek Chat APIs without using any PHP frameworks.

## Features

- Submit image URLs to Qwen VL Plus API for image analysis
- Process Qwen's response through DeepSeek Chat API
- Log all raw API responses to a log file
- Pure PHP implementation with no framework dependencies
- RESTful API interface
- Comprehensive error handling and logging

## Requirements

- PHP 7.4 or higher
- cURL extension enabled
- Write permissions for logs directory

## Setup

1. **Configure API Keys**
   
   Edit `config.php` and add your API keys:
   ```php
   'qwen' => [
       'api_key' => 'your-qwen-api-key-here',
       // ... other settings
   ],
   'deepseek' => [
       'api_key' => 'your-deepseek-api-key-here',
       // ... other settings
   ]
   ```

2. **Create Logs Directory**
   ```bash
   mkdir logs
   chmod 755 logs
   ```

## Usage

### Command Line Usage

Run the example script:
```bash
php example.php
```

### Web API Usage

1. **Start a web server:**
   ```bash
   php -S localhost:8000
   ```

2. **Check status:**
   ```bash
   curl http://localhost:8000/index.php
   ```

3. **Process an image:**
   ```bash
   curl -X POST http://localhost:8000/index.php \
        -H "Content-Type: application/json" \
        -d '{"image_url": "https://example.com/image.jpg"}'
   ```

## API Endpoints

### GET /index.php
Returns system status and configuration information.

**Response:**
```json
{
  "success": true,
  "message": "Image Processor API is running",
  "status": {
    "qwen_configured": true,
    "deepseek_configured": true,
    "log_file": "logs/api_responses.log",
    "log_file_exists": true
  }
}
```

### POST /index.php
Processes an image URL through the complete workflow.

**Request:**
```json
{
  "image_url": "https://example.com/image.jpg"
}
```

**Response:**
```json
{
  "success": true,
  "qwen_response": { ... },
  "deepseek_response": { ... },
  "qwen_text": "Extracted text from Qwen",
  "deepseek_text": "Processed text from DeepSeek"
}
```

## File Structure

- `config.php` - Configuration file with API settings
- `ApiClient.php` - HTTP client for making REST API calls
- `Logger.php` - Logging utility class
- `QwenService.php` - Qwen VL Plus API integration
- `DeepSeekService.php` - DeepSeek Chat API integration
- `ImageProcessor.php` - Main workflow orchestrator
- `index.php` - Web API entry point
- `example.php` - Command line example
- `logs/` - Directory for log files

## Configuration

The application uses the following technical constraints as specified:

### Qwen VL Plus API
- Model: qwen-vl-plus
- Role: system
- Content: "你是一个专业的图文专家。。。。。"
- Temperature: 0
- Detail: high

### DeepSeek Chat API
- Model: deepseek-chat
- Role: system
- Content: "你是一个专业的驾照科目考试专家。。。。"
- Temperature: 0

## Logging

All API responses are logged to `logs/api_responses.log` with timestamps and detailed information about each request and response.

## Error Handling

The application includes comprehensive error handling for:
- Invalid image URLs
- Missing API keys
- HTTP request failures
- JSON parsing errors
- API rate limits and errors

## License

This project is provided as-is for educational and development purposes.
