<?php

require_once 'ImageProcessor.php';

/**
 * Test script for the Image Processor
 */

echo "Image Processor Test Suite\n";
echo "==========================\n\n";

// Load configuration
$config = require 'config.php';

// Test 1: Configuration Loading
echo "Test 1: Configuration Loading\n";
if (is_array($config) && isset($config['qwen']) && isset($config['deepseek'])) {
    echo "✓ Configuration loaded successfully\n";
} else {
    echo "✗ Configuration loading failed\n";
    exit(1);
}

// Test 2: Class Instantiation
echo "\nTest 2: Class Instantiation\n";
try {
    $processor = new ImageProcessor($config);
    echo "✓ ImageProcessor instantiated successfully\n";
} catch (Exception $e) {
    echo "✗ ImageProcessor instantiation failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 3: Status Check
echo "\nTest 3: Status Check\n";
try {
    $status = $processor->getStatus();
    echo "✓ Status check successful\n";
    echo "  - Qwen configured: " . ($status['qwen_configured'] ? 'Yes' : 'No') . "\n";
    echo "  - DeepSeek configured: " . ($status['deepseek_configured'] ? 'Yes' : 'No') . "\n";
    echo "  - Log file: " . $status['log_file'] . "\n";
} catch (Exception $e) {
    echo "✗ Status check failed: " . $e->getMessage() . "\n";
}

// Test 4: URL Validation
echo "\nTest 4: URL Validation\n";
$testUrls = [
    'https://example.com/image.jpg' => true,
    'https://example.com/image.png' => true,
    'https://example.com/document.pdf' => false,
    'invalid-url' => false,
    '' => false
];

foreach ($testUrls as $url => $expected) {
    $result = $processor->validateImageUrl($url);
    if ($result === $expected) {
        echo "✓ URL validation for '{$url}': " . ($result ? 'valid' : 'invalid') . "\n";
    } else {
        echo "✗ URL validation for '{$url}' failed\n";
    }
}

// Test 5: API Client Test
echo "\nTest 5: API Client Test\n";
try {
    require_once 'ApiClient.php';
    $apiClient = new ApiClient($config);
    echo "✓ ApiClient instantiated successfully\n";
} catch (Exception $e) {
    echo "✗ ApiClient instantiation failed: " . $e->getMessage() . "\n";
}

// Test 6: Logger Test
echo "\nTest 6: Logger Test\n";
try {
    require_once 'Logger.php';
    $logger = new Logger($config);
    $logger->info("Test log message");
    echo "✓ Logger test successful\n";
    
    if (file_exists($config['logging']['log_file'])) {
        echo "✓ Log file created successfully\n";
    } else {
        echo "✗ Log file not created\n";
    }
} catch (Exception $e) {
    echo "✗ Logger test failed: " . $e->getMessage() . "\n";
}

// Test 7: Service Classes
echo "\nTest 7: Service Classes\n";
try {
    require_once 'QwenService.php';
    require_once 'DeepSeekService.php';
    
    $logger = new Logger($config);
    $qwenService = new QwenService($config, $logger);
    $deepSeekService = new DeepSeekService($config, $logger);
    
    echo "✓ QwenService instantiated successfully\n";
    echo "✓ DeepSeekService instantiated successfully\n";
} catch (Exception $e) {
    echo "✗ Service classes test failed: " . $e->getMessage() . "\n";
}

echo "\nTest Suite Completed\n";
echo "====================\n";

if (!$status['qwen_configured'] || !$status['deepseek_configured']) {
    echo "\nNote: To test the full workflow, please configure API keys in config.php\n";
} else {
    echo "\nAll tests passed! The system is ready for use.\n";
}
