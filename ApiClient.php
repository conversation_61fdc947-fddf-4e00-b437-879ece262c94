<?php

/**
 * HTTP API Client for making REST API calls
 */
class ApiClient
{
    private $config;
    
    public function __construct($config)
    {
        $this->config = $config;
    }
    
    /**
     * Make a POST request to the specified URL
     *
     * @param string $url
     * @param array $data
     * @param array $headers
     * @return array
     * @throws Exception
     */
    public function post($url, $data, $headers = [])
    {
        $ch = curl_init();
        
        // Default headers
        $defaultHeaders = [
            'Content-Type: application/json',
            'User-Agent: ' . $this->config['http']['user_agent']
        ];
        
        // Merge with custom headers
        $allHeaders = array_merge($defaultHeaders, $headers);
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['http']['timeout'],
            CURLOPT_HTTPHEADER => $allHeaders,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($response === false) {
            throw new Exception("cURL Error: " . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            throw new Exception("HTTP Error {$httpCode}: " . $response);
        }
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON Decode Error: " . json_last_error_msg());
        }
        
        return [
            'http_code' => $httpCode,
            'raw_response' => $response,
            'data' => $decodedResponse
        ];
    }
    
    /**
     * Make a GET request to the specified URL
     *
     * @param string $url
     * @param array $headers
     * @return array
     * @throws Exception
     */
    public function get($url, $headers = [])
    {
        $ch = curl_init();
        
        // Default headers
        $defaultHeaders = [
            'User-Agent: ' . $this->config['http']['user_agent']
        ];
        
        // Merge with custom headers
        $allHeaders = array_merge($defaultHeaders, $headers);
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['http']['timeout'],
            CURLOPT_HTTPHEADER => $allHeaders,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($response === false) {
            throw new Exception("cURL Error: " . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            throw new Exception("HTTP Error {$httpCode}: " . $response);
        }
        
        return [
            'http_code' => $httpCode,
            'raw_response' => $response,
            'data' => $decodedResponse
        ];
    }
}
