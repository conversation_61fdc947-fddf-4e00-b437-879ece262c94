<?php

/**
 * Simple logging utility class
 */
class Logger
{
    private $logFile;
    private $logLevel;
    
    const LEVELS = [
        'DEBUG' => 0,
        'INFO' => 1,
        'WARNING' => 2,
        'ERROR' => 3
    ];
    
    public function __construct($config)
    {
        $this->logFile = $config['logging']['log_file'];
        $this->logLevel = $config['logging']['log_level'];
        
        // Create logs directory if it doesn't exist
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * Log a message with the specified level
     *
     * @param string $level
     * @param string $message
     * @param array $context
     */
    public function log($level, $message, $context = [])
    {
        if (!isset(self::LEVELS[$level]) || !isset(self::LEVELS[$this->logLevel])) {
            return;
        }
        
        if (self::LEVELS[$level] < self::LEVELS[$this->logLevel]) {
            return;
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
        $logEntry = "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Log debug message
     */
    public function debug($message, $context = [])
    {
        $this->log('DEBUG', $message, $context);
    }
    
    /**
     * Log info message
     */
    public function info($message, $context = [])
    {
        $this->log('INFO', $message, $context);
    }
    
    /**
     * Log warning message
     */
    public function warning($message, $context = [])
    {
        $this->log('WARNING', $message, $context);
    }
    
    /**
     * Log error message
     */
    public function error($message, $context = [])
    {
        $this->log('ERROR', $message, $context);
    }
    
    /**
     * Log API response
     */
    public function logApiResponse($service, $response)
    {
        $this->info("API Response from {$service}", [
            'service' => $service,
            'http_code' => $response['http_code'],
            'raw_response' => $response['raw_response']
        ]);
    }
}
