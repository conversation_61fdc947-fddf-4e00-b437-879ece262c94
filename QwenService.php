<?php

require_once 'ApiClient.php';

/**
 * Qwen VL Plus API Service
 */
class QwenService
{
    private $config;
    private $apiClient;
    private $logger;
    
    public function __construct($config, $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
        $this->apiClient = new ApiClient($config);
    }
    
    /**
     * Process image URL with Qwen VL Plus
     *
     * @param string $imageUrl
     * @return array
     * @throws Exception
     */
    public function processImage($imageUrl)
    {
        if (empty($this->config['qwen']['api_key'])) {
            throw new Exception("Qwen API key is not configured");
        }
        
        $this->logger->info("Processing image with Qwen VL Plus", ['image_url' => $imageUrl]);
        
        $requestData = [
            'model' => $this->config['qwen']['model'],
            'input' => [
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $this->config['qwen']['system_content']
                    ],
                    [
                        'role' => 'user',
                        'content' => [
                            [
                                'type' => 'image_url',
                                'image_url' => [
                                    'url' => $imageUrl,
                                    'detail' => $this->config['qwen']['detail']
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'parameters' => [
                'temperature' => $this->config['qwen']['temperature']
            ]
        ];
        
        $headers = [
            'Authorization: Bearer ' . $this->config['qwen']['api_key'],
            'X-DashScope-Async: enable'
        ];
        
        try {
            $response = $this->apiClient->post(
                $this->config['qwen']['api_url'],
                $requestData,
                $headers
            );
            
            $this->logger->logApiResponse('Qwen VL Plus', $response);
            
            return $response;
            
        } catch (Exception $e) {
            $this->logger->error("Qwen API Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Extract text content from Qwen response
     *
     * @param array $response
     * @return string
     */
    public function extractTextFromResponse($response)
    {
        if (!isset($response['data']['output']['choices'][0]['message']['content'])) {
            return '';
        }
        
        return $response['data']['output']['choices'][0]['message']['content'];
    }
}
