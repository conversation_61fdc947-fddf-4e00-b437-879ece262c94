<?php

/**
 * Setup script to configure API keys
 */

echo "Image Processor Setup\n";
echo "=====================\n\n";

// Check if config file exists
if (!file_exists('config.php')) {
    echo "Error: config.php not found!\n";
    exit(1);
}

// Load current configuration
$config = require 'config.php';

echo "Current Configuration:\n";
echo "- Qwen API Key: " . (empty($config['qwen']['api_key']) ? 'Not configured' : 'Configured') . "\n";
echo "- DeepSeek API Key: " . (empty($config['deepseek']['api_key']) ? 'Not configured' : 'Configured') . "\n\n";

// Function to prompt for input
function promptInput($message, $default = '') {
    echo $message;
    if (!empty($default)) {
        echo " (current: " . substr($default, 0, 10) . "...)";
    }
    echo ": ";
    
    $input = trim(fgets(STDIN));
    return empty($input) ? $default : $input;
}

// Get API keys from user
echo "Enter your API keys (press Enter to keep current values):\n\n";

$qwenApiKey = promptInput("Qwen API Key", $config['qwen']['api_key']);
$deepSeekApiKey = promptInput("DeepSeek API Key", $config['deepseek']['api_key']);

// Update configuration
$config['qwen']['api_key'] = $qwenApiKey;
$config['deepseek']['api_key'] = $deepSeekApiKey;

// Write updated configuration
$configContent = "<?php\n\n/**\n * Configuration file for the image processing application\n */\n\nreturn " . var_export($config, true) . ";";

if (file_put_contents('config.php', $configContent)) {
    echo "\n✓ Configuration updated successfully!\n";
    
    // Test the configuration
    echo "\nTesting configuration...\n";
    
    require_once 'ImageProcessor.php';
    $processor = new ImageProcessor($config);
    $status = $processor->getStatus();
    
    echo "- Qwen API: " . ($status['qwen_configured'] ? '✓ Configured' : '✗ Not configured') . "\n";
    echo "- DeepSeek API: " . ($status['deepseek_configured'] ? '✓ Configured' : '✗ Not configured') . "\n";
    
    if ($status['qwen_configured'] && $status['deepseek_configured']) {
        echo "\n🎉 Setup completed! You can now use the image processor.\n";
        echo "\nNext steps:\n";
        echo "1. Run 'php example.php' to test with a sample image\n";
        echo "2. Start the web server with 'php -S localhost:8000'\n";
        echo "3. Check the README.md for detailed usage instructions\n";
    } else {
        echo "\n⚠️  Please ensure both API keys are configured correctly.\n";
    }
    
} else {
    echo "\n✗ Failed to update configuration file!\n";
    exit(1);
}

echo "\nSetup completed.\n";
