<?php

require_once 'ApiClient.php';

/**
 * DeepSeek Chat API Service
 */
class DeepSeekService
{
    private $config;
    private $apiClient;
    private $logger;
    
    public function __construct($config, $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
        $this->apiClient = new ApiClient($config);
    }
    
    /**
     * Process text with DeepSeek Chat
     *
     * @param string $text
     * @return array
     * @throws Exception
     */
    public function processText($text)
    {
        if (empty($this->config['deepseek']['api_key'])) {
            throw new Exception("DeepSeek API key is not configured");
        }
        
        $this->logger->info("Processing text with DeepSeek Chat", ['text_length' => strlen($text)]);
        
        $requestData = [
            'model' => $this->config['deepseek']['model'],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $this->config['deepseek']['system_content']
                ],
                [
                    'role' => 'user',
                    'content' => $text
                ]
            ],
            'temperature' => $this->config['deepseek']['temperature']
        ];
        
        $headers = [
            'Authorization: Bearer ' . $this->config['deepseek']['api_key']
        ];
        
        try {
            $response = $this->apiClient->post(
                $this->config['deepseek']['api_url'],
                $requestData,
                $headers
            );
            
            $this->logger->logApiResponse('DeepSeek Chat', $response);
            
            return $response;
            
        } catch (Exception $e) {
            $this->logger->error("DeepSeek API Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Extract text content from DeepSeek response
     *
     * @param array $response
     * @return string
     */
    public function extractTextFromResponse($response)
    {
        if (!isset($response['data']['choices'][0]['message']['content'])) {
            return '';
        }
        
        return $response['data']['choices'][0]['message']['content'];
    }
}
